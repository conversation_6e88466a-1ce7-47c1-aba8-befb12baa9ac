import type { Invoice } from "@/lib/types";
import { convertNumberToWords } from "@/lib/utils";
import {
  Document,
  Page,
  StyleSheet,
  Text,
  View,
  pdf,
} from "@react-pdf/renderer";
import React from "react";

const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#ffffff",
    padding: 32,
    fontSize: 10,
    fontFamily: "Helvetica",
    minHeight: "100vh",
  },
  mainContent: {
    flex: 1,
  },
  bottomSection: {
    marginTop: "auto",
  },
  header: {
    textAlign: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  companySection: {
    border: "1px solid #9CA3AF",
    marginBottom: 24,
  },
  companyHeader: {
    backgroundColor: "#F3F4F6",
    padding: 16,
    borderBottom: "1px solid #9CA3AF",
  },
  companyName: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  companyInfo: {
    fontSize: 12,
    marginBottom: 2,
  },
  billToSection: {
    flexDirection: "row",
  },
  billToLeft: {
    flex: 1,
    padding: 16,
    borderRight: "1px solid #9CA3AF",
  },
  billToRight: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
  },
  customerName: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 4,
  },
  customerInfo: {
    fontSize: 12,
    marginBottom: 2,
  },
  invoiceDetails: {
    fontSize: 12,
    marginBottom: 2,
  },
  itemsTable: {
    marginTop: 24,
    marginBottom: 24,
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: "#F3F4F6",
    borderTop: "1px solid #9CA3AF",
    borderLeft: "1px solid #9CA3AF",
    borderRight: "1px solid #9CA3AF",
    borderBottom: "1px solid #9CA3AF",
    padding: 8,
  },
  tableRow: {
    flexDirection: "row",
    borderLeft: "1px solid #9CA3AF",
    borderRight: "1px solid #9CA3AF",
    borderBottom: "1px solid #9CA3AF",
    padding: 8,
  },
  tableCell: {
    fontSize: 12,
    textAlign: "left",
  },
  tableCellCenter: {
    fontSize: 12,
    textAlign: "center",
  },
  tableCellRight: {
    fontSize: 12,
    textAlign: "right",
  },
  slNo: { width: "8%" },
  itemName: { width: "40%" },
  quantity: { width: "12%" },
  unit: { width: "10%" },
  price: { width: "15%" },
  amount: { width: "15%" },
  totalsSection: {
    flexDirection: "row",
    marginBottom: 24,
  },
  totalsLeft: {
    flex: 1,
  },
  totalsRight: {
    flex: 1,
    border: "1px solid #9CA3AF",
  },
  totalRow: {
    flexDirection: "row",
    borderBottom: "1px solid #9CA3AF",
  },
  totalLabel: {
    flex: 1,
    padding: 8,
    fontSize: 12,
  },
  totalValue: {
    flex: 1,
    padding: 8,
    fontSize: 12,
    textAlign: "right",
  },
  totalRowFinal: {
    flexDirection: "row",
  },
  totalLabelFinal: {
    flex: 1,
    padding: 8,
    fontSize: 12,
    fontWeight: "bold",
  },
  totalValueFinal: {
    flex: 1,
    padding: 8,
    fontSize: 12,
    fontWeight: "bold",
    textAlign: "right",
  },
  amountInWords: {
    padding: 8,
    borderTop: "1px solid #9CA3AF",
  },
  amountInWordsTitle: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 4,
  },
  amountInWordsText: {
    fontSize: 12,
  },
  footerSection: {
    flexDirection: "row",
    marginBottom: 24,
  },
  footerLeft: {
    flex: 1,
  },
  footerRight: {
    flex: 1,
    border: "1px solid #9CA3AF",
    padding: 16,
    textAlign: "right",
  },
  signatureTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 48,
  },
  signatureText: {
    fontSize: 12,
  },
  notesSection: {
    marginTop: 15,
  },
  notesTitle: {
    fontSize: 11,
    fontWeight: "bold",
    marginBottom: 4,
  },
  notesText: {
    fontSize: 10,
    color: "#666666",
  },
});

interface InvoicePDFProps {
  invoice: Invoice;
}

const InvoicePDF: React.FC<InvoicePDFProps> = ({ invoice }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Main Content */}
      <View style={styles.mainContent}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Invoice</Text>
        </View>

        {/* Company Info */}
        <View style={styles.companySection}>
          <View style={styles.companyHeader}>
            <Text style={styles.companyName}>EXPRESS PRINT</Text>
            <Text style={styles.companyInfo}>
              Panisheola Sitalatala, Haripal, Hooghly, Pin-712405
            </Text>
            <Text style={styles.companyInfo}>Phone: 9832407944</Text>
          </View>

          {/* Bill To and Invoice Details */}
          <View style={styles.billToSection}>
            <View style={styles.billToLeft}>
              <Text style={styles.sectionTitle}>Bill To:</Text>
              <Text style={styles.customerName}>{invoice.customer?.name}</Text>
              <Text style={styles.customerInfo}>{invoice.customer?.phone}</Text>
              {invoice.customer?.address && (
                <Text style={styles.customerInfo}>
                  {invoice.customer.address}
                </Text>
              )}
            </View>
            <View style={styles.billToRight}>
              <Text style={styles.sectionTitle}>Invoice Details:</Text>
              <Text style={styles.invoiceDetails}>
                No: {invoice.invoice_number}
              </Text>
              <Text style={styles.invoiceDetails}>
                Date: {new Date(invoice.date).toLocaleDateString("en-GB")}
              </Text>
            </View>
          </View>
        </View>

        {/* Items Table */}
        <View style={styles.itemsTable}>
          {/* Table Header */}
          <View style={styles.tableHeader}>
            <Text style={[styles.tableCell, styles.slNo]}>Sl. No.</Text>
            <Text style={[styles.tableCell, styles.itemName]}>Item Name</Text>
            <Text style={[styles.tableCellCenter, styles.quantity]}>Qty</Text>
            <Text style={[styles.tableCellCenter, styles.unit]}>Unit</Text>
            <Text style={[styles.tableCellRight, styles.price]}>
              Price (Rs.)
            </Text>
            <Text style={[styles.tableCellRight, styles.amount]}>
              Amount (Rs.)
            </Text>
          </View>

          {/* Table Rows */}
          {invoice.items?.map((item, index) => (
            <View key={item.id} style={styles.tableRow}>
              <Text style={[styles.tableCellCenter, styles.slNo]}>
                {index + 1}
              </Text>
              <Text style={[styles.tableCell, styles.itemName]}>
                {item.name}
              </Text>
              <Text style={[styles.tableCellCenter, styles.quantity]}>
                {item.quantity}
              </Text>
              <Text style={[styles.tableCellCenter, styles.unit]}>
                {item.unit}
              </Text>
              <Text style={[styles.tableCellRight, styles.price]}>
                {item.price.toFixed(2)}
              </Text>
              <Text style={[styles.tableCellRight, styles.amount]}>
                {item.amount.toFixed(2)}
              </Text>
            </View>
          ))}
        </View>

        {/* Notes */}
        {invoice.notes && (
          <View style={styles.notesSection}>
            <Text style={styles.notesTitle}>Notes:</Text>
            <Text style={styles.notesText}>{invoice.notes}</Text>
          </View>
        )}
      </View>

      {/* Bottom Section - Totals and Signature */}
      <View style={styles.bottomSection}>
        {/* Totals */}
        <View style={styles.totalsSection}>
          <View style={styles.totalsLeft}></View>
          <View style={styles.totalsRight}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Sub Total</Text>
              <Text style={styles.totalValue}>
                : Rs. {invoice.subtotal.toFixed(2)}
              </Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>
                Discount ({invoice.discount_percentage}%)
              </Text>
              <Text style={styles.totalValue}>
                : Rs. {invoice.discount.toFixed(2)}
              </Text>
            </View>
            <View style={styles.totalRowFinal}>
              <Text style={styles.totalLabelFinal}>Total</Text>
              <Text style={styles.totalValueFinal}>
                : Rs. {invoice.total.toFixed(2)}
              </Text>
            </View>
            <View style={styles.amountInWords}>
              <Text style={styles.amountInWordsTitle}>
                Invoice Amount In Words :
              </Text>
              <Text style={styles.amountInWordsText}>
                {convertNumberToWords(invoice.total)}
              </Text>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footerSection}>
          <View style={styles.footerLeft}></View>
          <View style={styles.footerRight}>
            <Text style={styles.signatureTitle}>For EXPRESS PRINT:</Text>
            <Text style={styles.signatureText}>Authorized Signatory</Text>
          </View>
        </View>
      </View>
    </Page>
  </Document>
);

export default InvoicePDF;

// Function to generate and download PDF
export const generateInvoicePDF = async (
  invoice: Invoice,
  filename: string
): Promise<void> => {
  try {
    const blob = await pdf(<InvoicePDF invoice={invoice} />).toBlob();

    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  }
};
